import { html } from 'hono/html';

/**
 * Kazeel + Platform logo component
 * Provides consistent branding across all UI states with uniform sizing
 */

export type LogoVariant = 'overlapping' | 'connected' | 'separated';

interface KazeelPlatformLogoProps {
  platformLogo?: string;
  platformName?: string;
  variant?: LogoVariant;
  className?: string;
}

/**
 * Consistent size classes for all logo instances
 */
const LOGO_CLASSES = {
  logo: 'w-12 h-12',
  connector: 'w-5 h-3',
};

/**
 * Render overlapping variant (default)
 */
function renderOverlapping(props: KazeelLogoProps, sizeClasses: ReturnType<typeof getSizeClasses>) {
  return html`
    <div class="flex flex-row items-center justify-center ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${sizeClasses.logo} mt-4 mb-2" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${sizeClasses.logo} mt-4 mb-2 z-[1] -m-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render connected variant (with link icon)
 */
function renderConnected(props: KazeelLogoProps, sizeClasses: ReturnType<typeof getSizeClasses>) {
  return html`
    <div class="flex flex-row items-center justify-center ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${sizeClasses.logo} mt-4 mb-2" />
      <img src="/link.png" alt="Connection Icon" class="${sizeClasses.connector} mt-3 mb-2 mx-4" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${sizeClasses.logo} mt-4 mb-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render separated variant (with spacing)
 */
function renderSeparated(props: KazeelLogoProps, sizeClasses: ReturnType<typeof getSizeClasses>) {
  return html`
    <div class="flex flex-row items-center justify-center space-x-4 ${props.className || ''}">
      <img src="/kazeel-logo.png" alt="Kazeel Logo" class="${sizeClasses.logo} mt-4 mb-2" />
      ${props.platformLogo
        ? html`
            <img
              src="${props.platformLogo}"
              alt="${props.platformName || 'Platform'} Logo"
              class="${sizeClasses.logo} mt-4 mb-2"
            />
          `
        : ''}
    </div>
  `;
}

/**
 * Render shimmer placeholder for loading states
 */
function renderShimmer(sizeClasses: ReturnType<typeof getSizeClasses>, className?: string) {
  return html`
    <div class="flex flex-row items-center justify-center ${className || ''}">
      <div class="shimmer ${sizeClasses.logo} mt-4 mb-2 rounded-full bg-neutral-20"></div>
      <div
        class="shimmer ${sizeClasses.logo} mt-4 mb-2 z-[1] -m-2 rounded-full bg-neutral-20"
      ></div>
    </div>
  `;
}

/**
 * Main Kazeel logo component
 */
export const KazeelLogo = (props: KazeelLogoProps = {}) => {
  const { size = 'medium', variant = 'overlapping', className } = props;

  const sizeClasses = getSizeClasses(size);

  // If no platform logo provided, show shimmer
  if (!props.platformLogo) {
    return renderShimmer(sizeClasses, className);
  }

  // Render based on variant
  switch (variant) {
    case 'connected':
      return renderConnected(props, sizeClasses);
    case 'separated':
      return renderSeparated(props, sizeClasses);
    case 'overlapping':
    default:
      return renderOverlapping(props, sizeClasses);
  }
};

/**
 * Convenience components for common use cases
 */
export const KazeelLogoSmall = (props: Omit<KazeelLogoProps, 'size'>) =>
  KazeelLogo({ ...props, size: 'small' });

export const KazeelLogoMedium = (props: Omit<KazeelLogoProps, 'size'>) =>
  KazeelLogo({ ...props, size: 'medium' });

export const KazeelLogoLarge = (props: Omit<KazeelLogoProps, 'size'>) =>
  KazeelLogo({ ...props, size: 'large' });

export const KazeelLogoConnected = (props: Omit<KazeelLogoProps, 'variant'>) =>
  KazeelLogo({ ...props, variant: 'connected' });
